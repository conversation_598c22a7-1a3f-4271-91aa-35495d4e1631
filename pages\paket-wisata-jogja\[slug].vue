<template>
  <div>
    <main>
      <div v-if="isLoading" class="container mx-auto px-4 py-16">
        <div class="text-center">
          <p class="text-lg text-gray-600">Memuat detail paket wisata...</p>
        </div>
      </div>

      <div v-else-if="error" class="container mx-auto px-4 py-16">
        <div class="text-center">
          <p class="text-lg text-red-600">{{ error }}</p>
        </div>
      </div>

      <!-- Filter Page (Duration or Category) -->
      <section v-else-if="isDuration || isCategory" class="bg-gray-100">
        <!-- Breadcrumb for Filter Pages -->
        <div class="bg-gray-50 py-4">
          <div class="container mx-auto px-4">
            <nav class="flex items-center space-x-2 text-sm">
              <NuxtLink to="/" class="text-gray-500 hover:text-gray-700">Home</NuxtLink>
              <span class="text-gray-400">/</span>
              <NuxtLink to="/paket-wisata-jogja/" class="text-gray-500 hover:text-gray-700">Paket Wisata Jogja</NuxtLink>
              <span class="text-gray-400">/</span>
              <span class="text-gray-900 font-medium">
                {{ isDuration ? getDurationLabel(slug) : getCategoryLabel(slug) }}
              </span>
            </nav>
          </div>
        </div>

        <div class="px-[9%] py-16">
          <div class="text-center mb-12">
            <h1 class="text-3xl font-bold mb-4">{{ pageTitle }}</h1>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
              {{ pageDescription }}
            </p>
          </div>

          <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filter -->
            <div class="lg:w-1/5">
              <PaketWisataFilter
                :active-category="activeCategory"
                :active-duration="activeDuration"
              />
            </div>

            <!-- Main Content -->
            <div class="lg:w-4/5">
              <!-- Paket Wisata Grid -->
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6">
                <div v-for="paket in filteredDestinations" :key="paket.id">
                  <PaketWisataCard :paket="paket" />
                </div>
              </div>

              <div v-if="filteredDestinations.length === 0" class="text-center py-12">
                <p class="text-lg text-gray-600">Tidak ada paket wisata yang sesuai dengan filter</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div v-else-if="isDetailPage && destination" class="bg-white">
        <!-- Breadcrumb -->
        <div class="bg-gray-50 py-4">
          <div class="container mx-auto px-4">
            <nav class="flex items-center space-x-2 text-sm">
              <NuxtLink to="/" class="text-gray-500 hover:text-gray-700">Home</NuxtLink>
              <span class="text-gray-400">/</span>
              <NuxtLink to="/paket-wisata-jogja/" class="text-gray-500 hover:text-gray-700">Paket Wisata Jogja</NuxtLink>
              <span class="text-gray-400">/</span>
              <NuxtLink
                :to="`/paket-wisata-jogja/${destination.category}`"
                class="text-gray-500 hover:text-gray-700"
              >
                {{ getCategoryLabel(destination.category) }}
              </NuxtLink>
              <span class="text-gray-400">/</span>
              <span class="text-gray-900 font-medium">{{ destination.title }}</span>
            </nav>
          </div>
        </div>

        <!-- Hero Section with Gallery -->
        <div class="container mx-auto px-4 pt-8">
          <div class="flex items-center justify-between mb-4">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900">
              {{ destination.title }}
            </h1>
            <div class="flex items-center gap-4">
              <button class="flex items-center gap-1 text-gray-600 hover:text-gray-900">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                </svg>
                Share
              </button>
              <button class="flex items-center gap-1 text-gray-600 hover:text-gray-900">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                Save
              </button>
            </div>
          </div>

          <!-- Location, Rating, Duration Info -->
          <div class="flex flex-wrap items-center gap-x-6 gap-y-2 mb-6 text-sm">
            <div class="flex items-center gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-gray-600">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
              </svg>
              <span class="text-gray-700">{{ destination.location }}</span>
            </div>
            <div class="flex items-center gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 text-yellow-400">
                <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
              </svg>
              <span class="text-gray-700">{{ destination.rating }} ({{ destination.reviews }} ulasan)</span>
            </div>
            <div class="flex items-center gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-gray-600">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="text-gray-700">{{ getDurationLabel(destination.duration) }}</span>
            </div>
          </div>

          <!-- Image Gallery -->
          <ImageGallery :images="getAllImages" :title="destination.title" />
        </div>

        <!-- Content Section -->
        <div class="container mx-auto px-4 py-8">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
              <div class="bg-white rounded-xl shadow-sm p-6 md:p-8 mb-6 md:mb-8">
                <h2 class="text-2xl font-bold mb-4">Tentang Paket Wisata Ini</h2>
                <p class="text-gray-600 mb-6">{{ destination.description || `Paket wisata ${destination.title} di Yogyakarta dengan fasilitas lengkap dan harga terjangkau. Nikmati pengalaman wisata terbaik dengan guide profesional, transportasi nyaman, dan itinerary yang telah dirancang khusus untuk memberikan pengalaman liburan yang tak terlupakan.` }}</p>

                <h3 class="text-xl font-bold mb-4">Fasilitas</h3>
                <ul class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <li v-for="(highlight, index) in destination.highlights"
                      :key="index"
                      class="flex items-center gap-2 text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-primary">
                      <path fill-rule="evenodd" d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                    </svg>
                    {{ highlight }}
                  </li>
                </ul>
              </div>

              <!-- Itinerary Section -->
              <div v-if="destination.itinerary" class="bg-white rounded-xl shadow-sm p-6 md:p-8 mb-6 md:mb-8">
                <h3 class="text-xl font-bold mb-6">Itinerary Perjalanan</h3>
                <div class="space-y-6">
                  <!-- Day 1 -->
                  <div v-if="destination.itinerary.day1" class="border-l-4 border-red-500 pl-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">{{ destination.itinerary.day1.title }}</h4>
                    <ul class="space-y-5">
                      <li v-for="(activity, index) in destination.itinerary.day1.activities" :key="index"
                          class="flex items-start gap-2 text-gray-600">
                        <span class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                        {{ activity }}
                      </li>
                    </ul>
                  </div>

                  <!-- Day 2 -->
                  <div v-if="destination.itinerary.day2" class="border-l-4 border-red-500 pl-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">{{ destination.itinerary.day2.title }}</h4>
                    <ul class="space-y-5">
                      <li v-for="(activity, index) in destination.itinerary.day2.activities" :key="index"
                          class="flex items-start gap-2 text-gray-600">
                        <span class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                        {{ activity }}
                      </li>
                    </ul>
                  </div>

                  <!-- Day 3 -->
                  <div v-if="destination.itinerary.day3" class="border-l-4 border-red-500 pl-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">{{ destination.itinerary.day3.title }}</h4>
                    <ul class="space-y-5">
                      <li v-for="(activity, index) in destination.itinerary.day3.activities" :key="index"
                          class="flex items-start gap-2 text-gray-600">
                        <span class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                        {{ activity }}
                      </li>
                    </ul>
                  </div>

                  <!-- Day 4 -->
                  <div v-if="destination.itinerary.day4" class="border-l-4 border-red-500 pl-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">{{ destination.itinerary.day4.title }}</h4>
                    <ul class="space-y-5">
                      <li v-for="(activity, index) in destination.itinerary.day4.activities" :key="index"
                          class="flex items-start gap-2 text-gray-600">
                        <span class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                        {{ activity }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Category & Duration Section -->
              <div class="bg-white rounded-xl shadow-sm p-6 md:p-8 mb-6 md:mb-8">
                <h3 class="text-xl font-bold mb-4">Kategori & Durasi</h3>
                <div class="flex flex-wrap gap-2">
                  <NuxtLink
                    :to="`/paket-wisata-jogja/${destination.category}`"
                    class="px-3 py-1 bg-primary/10 text-primary text-sm rounded-full hover:bg-primary/20 transition-colors"
                  >
                    {{ getCategoryLabel(destination.category) }}
                  </NuxtLink>

                  <span
                    class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                  >
                    {{ getDurationLabel(destination.duration) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-xl shadow-sm p-6 md:p-8 sticky top-8">
                <div class="flex items-center justify-between mb-4">
                  <div>
                    <span class="text-gray-500 text-sm">Harga Paket Wisata</span>
                    <div class="text-2xl font-bold text-primary">{{ selectedPricing?.price || destination.price }}</div>
                  </div>
                  <!-- <div class="text-right">
                    <div class="flex items-center text-yellow-500 mb-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                      </svg>
                      <span class="font-medium ml-1">{{ destination.rating }}</span>
                    </div>
                    <span class="text-sm text-gray-500">{{ destination.reviews }} ulasan</span>
                  </div> -->
                </div>

                <!-- Pilihan Harga berdasarkan Jumlah Orang -->
                <div v-if="destination.pricing && destination.pricing.length > 0" class="mb-6">
                  <h3 class="font-bold mb-3">Pilihan Paket</h3>
                  <div class="space-y-5">
                    <div 
                      v-for="pricing in destination.pricing" 
                      :key="pricing.pax"
                      class="relative"
                    >
                      <input
                        :id="`pricing-${pricing.pax}`"
                        v-model="selectedPricing"
                        :value="pricing"
                        type="radio"
                        name="pricing"
                        class="sr-only"
                      />
                      <label
                        :for="`pricing-${pricing.pax}`"
                        class="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                        :class="{
                          'border-primary bg-primary/5': selectedPricing?.pax === pricing.pax,
                          'border-gray-200': selectedPricing?.pax !== pricing.pax
                        }"
                      >
                        <div class="flex items-center">
                          <div class="flex items-center justify-center w-4 h-4 border-2 rounded-full mr-3"
                               :class="{
                                 'border-primary bg-primary': selectedPricing?.pax === pricing.pax,
                                 'border-gray-300': selectedPricing?.pax !== pricing.pax
                               }">
                            <div v-if="selectedPricing?.pax === pricing.pax" class="w-2 h-2 bg-white rounded-full"></div>
                          </div>
                          <div>
                            <div class="font-medium text-gray-900">{{ pricing.label }}</div>
                            <div class="text-sm text-gray-500">Per orang</div>
                          </div>
                        </div>
                        <div class="text-lg font-bold text-primary">{{ pricing.price }}</div>
                      </label>
                    </div>
                  </div>
                </div>

                <!-- Booking Buttons -->
                <div class="space-y-3 mb-6">
                  <button
                    @click="openWhatsApp"
                    class="w-full bg-primary text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-dark transition-colors"
                  >
                    Pesan Sekarang
                  </button>
                  <button
                    @click="openConsultation"
                    class="w-full border-2 border-primary text-primary py-3 px-6 rounded-lg font-medium hover:bg-primary/10 transition-colors"
                  >
                    Konsultasi Gratis
                  </button>
                </div>

                <!-- <div class="border-t border-gray-200 pt-6">
                  <h3 class="font-bold mb-4">Fasilitas</h3>
                  <ul class="space-y-3">
                    <li class="flex items-center gap-2 text-gray-600">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-green-500">
                        <path fill-rule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clip-rule="evenodd" />
                      </svg>
                      Mobil
                    </li>
                    <li class="flex items-center gap-2 text-gray-600">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-green-500">
                        <path fill-rule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clip-rule="evenodd" />
                      </svg>
                      Driver
                    </li>
                    <li class="flex items-center gap-2 text-gray-600">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-green-500">
                        <path fill-rule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clip-rule="evenodd" />
                      </svg>
                      BBM
                    </li>
                    <li class="flex items-center gap-2 text-gray-600">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-green-500">
                        <path fill-rule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clip-rule="evenodd" />
                      </svg>
                      Tiket Wisata
                    </li>
                  </ul>
                </div> -->

                <!-- Contact Info -->
                <div class="border-t border-gray-200 pt-6 mt-6">
                  <div class="text-center">
                    <p class="text-sm text-gray-600 mb-2">Butuh bantuan?</p>
                    <a href="tel:+6285186888837" class="text-primary font-medium hover:text-primary-dark">
                      + 62 8518 6888 837
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Related Packages -->
          <div class="mt-12">
            <h2 class="text-2xl font-bold mb-6"><span class="text-primary"><NuxtLink to="/paket-wisata-jogja">Paket Trip Jogja</NuxtLink></span> Lain nya</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div v-for="paket in relatedPackages" :key="paket.id">
                <PaketWisataCard :paket="paket" />
              </div>
            </div>
          </div>

          <!-- Sewa Mobil Wisata Jogja -->
          <div class="mt-16 border-t border-gray-200 pt-12">
            <div class="flex items-center justify-between mb-6">
              <div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2"><span class="text-primary"><NuxtLink to="/sewa-mobil-jogja">Sewa Mobil untuk Traveling di Jogja</NuxtLink></span></h2>
                <p class="text-gray-600">Lengkapi paket wisata Anda dengan sewa mobil terpercaya untuk kenyamanan perjalanan</p>
              </div>
              <NuxtLink 
                to="/sewa-mobil-jogja/" 
                class="hidden md:flex items-center gap-2 text-primary hover:text-primary-dark font-medium transition-colors"
              >
                Lihat Semua
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
                </svg>
              </NuxtLink>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
              <div v-for="mobil in popularCars" :key="mobil.id">
                <SewaMobilCard :mobil="mobil" />
              </div>
            </div>

            <!-- Mobile "Lihat Semua" button -->
            <div class="md:hidden text-center">
              <NuxtLink 
                to="/sewa-mobil-jogja/" 
                class="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-dark transition-colors"
              >
                Lihat Semua Mobil
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
                </svg>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="container mx-auto px-4 py-16">
        <div class="text-center">
          <p class="text-lg text-gray-600">Paket wisata tidak ditemukan</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useHead } from '#imports'
import { usePaketWisata } from '~/composables/usePaketWisata'
import { useImageWithFallback } from '~/composables/useImageWithFallback'
import ImageGallery from '~/components/ImageGallery.vue'
import SewaMobilCard from '~/components/SewaMobilCard.vue'
import paketWisataData from '~/data/paket-wisata.json'
import sewaMobilData from '~/data/sewa-mobil.json'

const route = useRoute()
const {
  getCategoryLabel,
  getDurationLabel,
  getAllDurations,
  getAllCategories,
  getPaketWisataByDuration,
  getPaketWisataByCategory
} = usePaketWisata()
const { getSafeImageUrl } = useImageWithFallback()

// State
const isLoading = ref(true)
const error = ref(null)
const paketWisata = ref(paketWisataData)
const selectedPricing = ref(null)

// Get slug from route
const slug = computed(() => route.params.slug)

// Determine what type of page this is
const isDuration = computed(() => {
  const durations = getAllDurations.value.map(d => d.slug)
  return durations.includes(slug.value)
})

const isCategory = computed(() => {
  const categories = getAllCategories.value.map(c => c.slug)
  return categories.includes(slug.value)
})

const isDetailPage = computed(() => {
  return !isDuration.value && !isCategory.value
})

// Get current destination (for detail page)
const destination = computed(() => {
  if (!isDetailPage.value) return null
  return typeof slug.value === 'string' ? paketWisata.value.find(item => item.slug === slug.value) : undefined
})

// Get filtered destinations (for filter pages)
const filteredDestinations = ref([])
const activeCategory = ref('')
const activeDuration = ref('')

// Get page title for filter pages
const pageTitle = computed(() => {
  if (!slug.value) return 'Paket Wisata Jogja'

  if (isDuration.value) {
    return `Paket Wisata Jogja ${getDurationLabel(slug.value)}`
  } else if (isCategory.value) {
    const labels = {
      'tanpa-hotel': 'Paket Wisata Jogja Tanpa Hotel',
      'dengan-hotel': 'Paket Wisata Jogja Dengan Hotel',
      'honeymoon': 'Paket Honeymoon Jogja',
      'gathering': 'Paket Gathering Jogja'
    }
    return labels[slug.value] || 'Paket Wisata Jogja'
  }

  return 'Paket Wisata Jogja'
})

// Get page description for filter pages
const pageDescription = computed(() => {
  if (!slug.value) return 'Temukan paket wisata Jogja yang sesuai dengan kebutuhan dan preferensi Anda'

  if (isDuration.value) {
    return `Pilihan paket wisata Jogja dengan durasi ${getDurationLabel(slug.value)} untuk pengalaman liburan Anda di Yogyakarta`
  } else if (isCategory.value) {
    const descriptions = {
      'tanpa-hotel': 'Pilihan paket wisata Jogja tanpa akomodasi hotel dengan harga terjangkau',
      'dengan-hotel': 'Paket wisata Jogja lengkap dengan akomodasi hotel bintang untuk kenyamanan Anda',
      'honeymoon': 'Paket romantis untuk pasangan pengantin baru dengan pengalaman tak terlupakan',
      'gathering': 'Paket khusus untuk acara gathering perusahaan atau kelompok dengan fasilitas lengkap'
    }
    return descriptions[slug.value] || 'Temukan paket wisata Jogja yang sesuai dengan kebutuhan dan preferensi Anda'
  }

  return 'Temukan paket wisata Jogja yang sesuai dengan kebutuhan dan preferensi Anda'
})

// Fetch data based on page type
const fetchData = async () => {
  try {
    isLoading.value = true
    error.value = null

    console.log('Fetching data for slug:', slug.value, 'isDuration:', isDuration.value, 'isCategory:', isCategory.value, 'isDetailPage:', isDetailPage.value)

    if (isDuration.value) {
      // Handle duration filtering
      activeDuration.value = slug.value
      const durationData = getPaketWisataByDuration(slug.value)

      if (durationData && durationData.length > 0) {
        console.log('Duration data fetched:', durationData)
        filteredDestinations.value = durationData
      } else {
        error.value = 'Tidak ditemukan paket wisata dengan durasi ini'
      }
    } else if (isCategory.value) {
      // Handle category filtering
      activeCategory.value = slug.value
      const categoryData = getPaketWisataByCategory(slug.value)

      if (categoryData && categoryData.length > 0) {
        console.log('Category data fetched:', categoryData)
        filteredDestinations.value = categoryData
      } else {
        error.value = 'Tidak ditemukan paket wisata dengan kategori ini'
      }
    } else if (isDetailPage.value) {
      // Handle detail page
      if (!destination.value) {
        error.value = 'Paket wisata tidak ditemukan'
      }
    } else {
      error.value = 'Halaman tidak ditemukan'
    }
  } catch (err) {
    console.error('Error in fetchData:', err)
    error.value = err.message || 'Terjadi kesalahan saat memuat data'
  } finally {
    isLoading.value = false
  }
}

// Set loading to false after component is mounted
onMounted(() => {
  console.log('Page mounted, slug:', route.params.slug)
  console.log('Destination:', destination.value)
  fetchData()
})

// Set default selected pricing when destination changes
watch(destination, (newDestination) => {
  if (newDestination && newDestination.pricing && newDestination.pricing.length > 0) {
    selectedPricing.value = newDestination.pricing[0]
  }
}, { immediate: true })

// Get all images for gallery
const getAllImages = computed(() => {
  if (!destination.value) return []

  // Pastikan gambar utama ada di posisi pertama
  const mainImage = destination.value.image

  // Jika tidak ada array images, gunakan gambar utama dan duplikat
  if (!destination.value.images || destination.value.images.length === 0) {
    // Gunakan gambar utama dan duplikat untuk memastikan ada 5 gambar
    return [
      mainImage,
      mainImage,
      mainImage,
      mainImage,
      mainImage
    ]
  }

  // Gabungkan gambar utama dengan array images
  // Pastikan gambar utama ada di posisi pertama
  const result = [mainImage]

  // Tambahkan gambar dari array images
  // Tidak perlu menghapus duplikat, karena kita ingin menampilkan 5 gambar
  for (let i = 0; i < destination.value.images.length && result.length < 5; i++) {
    result.push(destination.value.images[i])
  }

  // Jika masih kurang dari 5 gambar, duplikat gambar yang ada
  while (result.length < 5) {
    // Gunakan gambar yang sudah ada untuk mengisi slot yang kosong
    const index = (result.length - 1) % (result.length) + 1
    result.push(result[index - 1])
  }

  console.log('getAllImages - result:', result)
  return result
})

// Get related packages
const relatedPackages = computed(() => {
  if (!destination.value) return []

  // Get packages with same category
  let related = paketWisata.value.filter(item => item.category === destination.value.category)

  // Filter out current package
  related = related.filter(p => p.id !== destination.value.id)

  // If we don't have enough related packages, add some with same duration
  if (related.length < 3) {
    const durationRelated = paketWisata.value.filter(item =>
      item.duration === destination.value.duration &&
      item.id !== destination.value.id &&
      !related.some(r => r.id === item.id)
    )

    related = [...related, ...durationRelated]
  }

  // If we still don't have enough, add random packages
  if (related.length < 3) {
    const randomPackages = paketWisata.value
      .filter(p =>
        p.id !== destination.value.id &&
        !related.some(r => r.id === p.id)
      )
      .sort(() => 0.5 - Math.random())

    related = [...related, ...randomPackages]
  }

  // Return at most 3 related packages
  return related.slice(0, 3)
})

// Get popular car rentals (3 cars with highest rating and reviews)
const popularCars = computed(() => {
  if (!sewaMobilData?.sewaMobil) return []
  
  return sewaMobilData.sewaMobil
    .filter(mobil => mobil.rating >= 4.7) // Filter high rated cars
    .sort((a, b) => {
      // Sort by rating first, then by reviews count
      if (b.rating !== a.rating) {
        return b.rating - a.rating
      }
      return b.reviews - a.reviews
    })
    .slice(0, 3) // Get top 3 cars
})

// Open WhatsApp for booking
const openWhatsApp = () => {
  if (!destination.value || !selectedPricing.value) return
  
  const packageDetails = `
🌟 *${destination.value.title}*

📍 Lokasi: ${destination.value.location}
⏰ Durasi: ${getDurationLabel(destination.value.duration)}
👥 Jumlah Peserta: ${selectedPricing.value.label}
💰 Harga: ${selectedPricing.value.price} per orang

✨ *Highlight Paket:*
${destination.value.highlights.map(highlight => `• ${highlight}`).join('\n')}

📞 Saya tertarik untuk booking paket ini. Bisa minta informasi lebih detail dan cara pemesanannya?`

  const whatsappUrl = `https://wa.me/6285186888837?text=${encodeURIComponent(packageDetails)}`
  window.open(whatsappUrl, '_blank')
}

// Open WhatsApp for consultation
const openConsultation = () => {
  if (!destination.value) return
  
  const consultationMessage = `Halo! 👋

Saya tertarik untuk diskusi tentang *${destination.value.title}*.

Bisa bantuin saya untuk:
• Konsultasi detail paket wisata
• Customisasi itinerary sesuai kebutuhan
• Informasi harga dan fasilitas lengkap
• Tips dan rekomendasi untuk perjalanan

Terima kasih! 🙏`

  const whatsappUrl = `https://wa.me/6285186888837?text=${encodeURIComponent(consultationMessage)}`
  window.open(whatsappUrl, '_blank')
}

// Schema JSON-LD untuk halaman paket wisata - SINGLE SOURCE OF TRUTH
// CATATAN: Composable useSchemaStructured.js TIDAK DIGUNAKAN di halaman ini untuk menghindari duplikasi
const schemaData = computed(() => {
  if (isDetailPage.value && destination.value) {
    return {
      "@context": "https://schema.org",
      "@graph": [
        // Organization Schema
        {
          "@type": "Organization",
          "@id": "https://www.jogjaliburan.com/#organization",
          "name": "3J Tour - Jogja Liburan",
          "alternateName": "3J Tour",
          "url": "https://www.jogjaliburan.com/",
          "logo": {
            "@type": "ImageObject",
            "url": "https://www.jogjaliburan.com/3j-tour-logo.webp",
            "width": 300,
            "height": 100
          },
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+62-851-8688-8837",
            "contactType": "customer service",
            "areaServed": "ID",
            "availableLanguage": ["Indonesian"]
          },
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "Jl. Merpati, Modalan, Banguntapan, Kec. Banguntapan",
            "addressLocality": "Banguntapan",
            "addressRegion": "Kabupaten Bantul, Daerah Istimewa Yogyakarta",
            "postalCode": "55198",
            "addressCountry": "ID"
          },
          "sameAs": [
            "https://wa.me/6285186888837"
          ]
        },
        // Service Schema untuk paket wisata spesifik - ONLY ONE aggregateRating per page
        {
          "@type": "Service",
          "@id": `https://www.jogjaliburan.com/paket-wisata-jogja/${destination.value.slug}/#service`,
          "name": destination.value.title,
          "description": `${destination.value.description || `Paket wisata ${destination.value.title} di Yogyakarta dengan fasilitas lengkap dan harga terjangkau. Nikmati pengalaman wisata terbaik dengan guide profesional dan transportasi nyaman.`} Paket wisata lengkap dengan guide profesional, transportasi nyaman, dan itinerary terbaik untuk pengalaman liburan tak terlupakan di Yogyakarta.`,
          "image": destination.value.image,
          "url": `https://www.jogjaliburan.com/paket-wisata-jogja/${destination.value.slug}/`,
          "provider": {
            "@id": "https://www.jogjaliburan.com/#organization"
          },
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "Jl. Merpati, Modalan, Banguntapan, Kec. Banguntapan",
            "addressLocality": "Banguntapan",
            "addressRegion": "Kabupaten Bantul, Daerah Istimewa Yogyakarta",
            "postalCode": "55198",
            "addressCountry": "ID"
          },
          "areaServed": {
            "@type": "State",
            "name": "DI Yogyakarta"
          },
          "serviceType": "Tour Package",
          "category": [
            "Wisata Budaya",
            "Wisata Alam",
            "Wisata Kuliner",
            "Wisata Edukasi",
            "Paket Custom Tour"
          ],
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": `Paket ${destination.value.title}`,
            "itemListElement": destination.value.pricing && destination.value.pricing.length > 0
              ? destination.value.pricing.map(pricing => ({
                  "@type": "Offer",
                  "name": pricing.label,
                  "description": `${destination.value.title} untuk ${pricing.label}`,
                  "price": pricing.price?.replace(/[^\d]/g, '') || "350000",
                  "priceCurrency": "IDR",
                  "availability": "https://schema.org/InStock",
                  "validFrom": new Date().toISOString().split('T')[0],
                  "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                  "seller": {
                    "@id": "https://www.jogjaliburan.com/#organization"
                  },
                  "itemOffered": {
                    "@type": "Service",
                    "name": `${destination.value.title} - ${pricing.label}`,
                    "description": destination.value.description
                  }
                }))
              : [{
                  "@type": "Offer",
                  "name": destination.value.title,
                  "price": destination.value.price?.replace(/[^\d]/g, '') || "350000",
                  "priceCurrency": "IDR",
                  "availability": "https://schema.org/InStock",
                  "validFrom": new Date().toISOString().split('T')[0],
                  "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                  "seller": {
                    "@id": "https://www.jogjaliburan.com/#organization"
                  },
                  "itemOffered": {
                    "@type": "Service",
                    "name": destination.value.title,
                    "description": destination.value.description
                  }
                }]
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": destination.value.rating || 4.5,
            "reviewCount": destination.value.reviews || 50,
            "bestRating": 5,
            "worstRating": 1,
            "itemReviewed": {
              "@type": "Trip",
              "@id": `https://www.jogjaliburan.com/paket-wisata-jogja/${destination.value.slug}/#trip`
            }
          }
        },
        // Trip/Tour Schema untuk informasi detail wisata - NO aggregateRating to avoid duplication
        {
          "@type": "Trip",
          "@id": `https://www.jogjaliburan.com/paket-wisata-jogja/${destination.value.slug}/#trip`,
          "name": destination.value.title,
          "description": destination.value.description,
          "image": destination.value.image,
          "duration": `P${destination.value.duration?.includes('1-hari') ? '1' : destination.value.duration?.includes('2-hari') ? '2' : destination.value.duration?.includes('3-hari') ? '3' : destination.value.duration?.includes('4-hari') ? '4' : '1'}D`,
          "location": {
            "@type": "Place",
            "name": destination.value.location || "Yogyakarta",
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "Jl. Merpati, Modalan, Banguntapan, Kec. Banguntapan",
              "addressLocality": "Banguntapan",
              "addressRegion": "Kabupaten Bantul, Daerah Istimewa Yogyakarta",
              "postalCode": "55198",
              "addressCountry": "ID"
            }
          },
          "offers": destination.value.pricing && destination.value.pricing.length > 0
            ? destination.value.pricing.map(pricing => ({
                "@type": "Offer",
                "price": pricing.price?.replace(/[^\d]/g, '') || "350000",
                "priceCurrency": "IDR",
                "availability": "https://schema.org/InStock"
              }))
            : [{
                "@type": "Offer",
                "price": destination.value.price?.replace(/[^\d]/g, '') || "350000",
                "priceCurrency": "IDR",
                "availability": "https://schema.org/InStock"
              }]
        },
        // Breadcrumb Schema
        {
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "Home",
              "item": "https://www.jogjaliburan.com/"
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "Paket Wisata Jogja",
              "item": "https://www.jogjaliburan.com/paket-wisata-jogja/"
            },
            {
              "@type": "ListItem",
              "position": 3,
              "name": destination.value.title,
              "item": `https://www.jogjaliburan.com/paket-wisata-jogja/${destination.value.slug}/`
            }
          ]
        }
      ]
    }
  } else if (isDuration.value || isCategory.value) {
    return {
      "@context": "https://schema.org",
      "@graph": [
        // Organization Schema
        {
          "@type": "Organization",
          "@id": "https://www.jogjaliburan.com/#organization",
          "name": "3J Tour - Jogja Liburan",
          "alternateName": "3J Tour",
          "url": "https://www.jogjaliburan.com/",
          "logo": {
            "@type": "ImageObject",
            "url": "https://www.jogjaliburan.com/3j-tour-logo.webp",
            "width": 300,
            "height": 100
          },
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+62-851-8688-8837",
            "contactType": "customer service",
            "areaServed": "ID",
            "availableLanguage": ["Indonesian"]
          },
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "Jl. Merpati, Modalan, Banguntapan, Kec. Banguntapan",
            "addressLocality": "Banguntapan",
            "addressRegion": "Kabupaten Bantul, Daerah Istimewa Yogyakarta",
            "postalCode": "55198",
            "addressCountry": "ID"
          },
          "sameAs": [
            "https://wa.me/6285186888837"
          ]
        },
        // Collection Page Schema untuk filter pages
        {
          "@type": "CollectionPage",
          "@id": `https://www.jogjaliburan.com/paket-wisata-jogja/${slug.value}/#collection`,
          "name": pageTitle.value,
          "description": pageDescription.value,
          "url": `https://www.jogjaliburan.com/paket-wisata-jogja/${slug.value}/`,
          "mainEntity": {
            "@type": "ItemList",
            "numberOfItems": filteredDestinations.value.length,
            "itemListElement": filteredDestinations.value.map((paket, index) => ({
              "@type": "ListItem",
              "position": index + 1,
              "item": {
                "@type": "Service",
                "@id": `https://www.jogjaliburan.com/paket-wisata-jogja/${paket.slug}/#service`,
                "name": paket.title,
                "description": paket.description,
                "image": paket.image,
                "url": `https://www.jogjaliburan.com/paket-wisata-jogja/${paket.slug}/`,
                "provider": {
                  "@id": "https://www.jogjaliburan.com/#organization"
                }
              }
            }))
          }
        },
        // Breadcrumb Schema
        {
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "Home",
              "item": "https://www.jogjaliburan.com/"
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "Paket Wisata Jogja",
              "item": "https://www.jogjaliburan.com/paket-wisata-jogja/"
            },
            {
              "@type": "ListItem",
              "position": 3,
              "name": pageTitle.value,
              "item": `https://www.jogjaliburan.com/paket-wisata-jogja/${slug.value}/`
            }
          ]
        }
      ]
    }
  }
  return null
})

// SEO metadata
useHead(() => ({
  title: destination.value ? `${destination.value.title} | Paket Wisata Jogja` : pageTitle.value,
  meta: [
    { name: 'description', content: destination.value?.description || pageDescription.value },
    // Open Graph / Facebook
    { property: 'og:type', content: 'service' },
    { property: 'og:url', content: `https://www.jogjaliburan.com/paket-wisata-jogja/${route.params.slug}/` },
    { property: 'og:title', content: destination.value?.title || pageTitle.value },
    { property: 'og:description', content: destination.value?.description || pageDescription.value },
    { property: 'og:image', content: destination.value?.image || '' },
    // Twitter
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:url', content: `https://www.jogjaliburan.com/paket-wisata-jogja/${route.params.slug}/` },
    { name: 'twitter:title', content: destination.value?.title || pageTitle.value },
    { name: 'twitter:description', content: destination.value?.description || pageDescription.value },
    { name: 'twitter:image', content: destination.value?.image || '' }
  ],
  link: [
    { rel: 'canonical', href: `https://www.jogjaliburan.com/paket-wisata-jogja/${route.params.slug}/` }
  ],
  script: schemaData.value ? [
    {
      type: 'application/ld+json',
      children: JSON.stringify(schemaData.value)
    }
  ] : []
}))
</script>