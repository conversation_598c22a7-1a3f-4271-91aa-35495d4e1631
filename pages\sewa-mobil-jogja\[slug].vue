<template>
  <div>
    <!-- Car Detail Page -->
    <div v-if="car" class="bg-white">
      <!-- Breadcrumb -->
      <div class="bg-gray-50 py-4">
        <div class="container mx-auto px-4">
          <nav class="flex items-center space-x-2 text-sm">
            <NuxtLink to="/" class="text-gray-500 hover:text-gray-700">Home</NuxtLink>
            <span class="text-gray-400">/</span>
            <NuxtLink to="/sewa-mobil-jogja/" class="text-gray-500 hover:text-gray-700">Sewa Mobil Jogja</NuxtLink>
            <span class="text-gray-400">/</span>
            <span class="text-gray-900 font-medium">{{ car.name }}</span>
          </nav>
        </div>
      </div>

      <!-- Hero Section with Gallery -->
      <div class="container mx-auto px-4 pt-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <!-- Car Image -->
          <div class="space-y-4">
            <div class="aspect-w-16 aspect-h-12 rounded-xl overflow-hidden">
              <img 
                :src="car.image" 
                :alt="car.name"
                class="w-full h-96 object-cover rounded-xl"
              />
            </div>
          </div>

          <!-- Car Info -->
          <div class="space-y-6">
            <div>
              <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{{ car.name }}</h1>
              <p class="text-lg text-gray-600 leading-relaxed">{{ car.description }}</p>
            </div>

            <!-- Rating -->
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-1">
                <div class="flex text-yellow-400">
                  <svg v-for="star in 5" :key="star" class="w-5 h-5 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
                <span class="text-lg font-semibold text-gray-900">{{ car.rating }}</span>
              </div>
              <span class="text-gray-600">({{ car.reviews }} ulasan)</span>
            </div>

            <!-- Car Specifications -->
            <div class="bg-gray-50 rounded-xl p-6">
              <h3 class="text-xl font-semibold text-gray-900 mb-4">Spesifikasi Kendaraan</h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                  <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div class="font-semibold text-gray-900">{{ car.capacity }}</div>
                  <div class="text-sm text-gray-600">Kapasitas</div>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div class="font-semibold text-gray-900">{{ car.transmission }}</div>
                  <div class="text-sm text-gray-600">Transmisi</div>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div class="font-semibold text-gray-900">{{ car.year }}</div>
                  <div class="text-sm text-gray-600">Tahun</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="container mx-auto px-4 pb-16">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Left Content -->
          <div class="lg:col-span-2 space-y-8">
            <!-- Package Selection Section -->
            <div class="bg-white rounded-xl shadow-lg p-6">
              <h2 class="text-2xl font-bold text-gray-900 mb-6">Pilih Paket Sewa</h2>

              <!-- Package Options -->
              <div class="space-y-4 mb-6">
                <div
                  v-for="(pkg, key) in car.packages"
                  :key="key"
                  @click="selectedPackage = key"
                  :class="[
                    'border-2 rounded-xl p-4 cursor-pointer transition-all duration-200',
                    selectedPackage === key
                      ? 'border-primary bg-primary/5'
                      : 'border-gray-200 hover:border-primary/50'
                  ]"
                >
                  <div class="flex items-center justify-between">
                    <div class="flex-1">
                      <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ pkg.name }}</h3>
                      <p class="text-gray-600 text-sm">{{ pkg.description }}</p>
                    </div>
                    <div class="text-right ml-4">
                      <div class="text-sm text-gray-500">Mulai dari</div>
                      <div class="text-xl font-bold text-primary">{{ formatPrice(pkg.pricing['12jam']) }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Duration Selection -->
              <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Pilih Durasi</h3>
                <div class="grid grid-cols-2 gap-4">
                  <button
                    @click="selectedDuration = '12jam'"
                    :class="[
                      'p-4 rounded-lg border-2 transition-all duration-200',
                      selectedDuration === '12jam'
                        ? 'border-primary bg-primary text-white'
                        : 'border-gray-200 hover:border-primary/50'
                    ]"
                  >
                    <div class="text-center">
                      <div class="font-semibold">12 Jam</div>
                      <div class="text-sm opacity-90">Half Day</div>
                    </div>
                  </button>
                  <button
                    @click="selectedDuration = '24jam'"
                    :class="[
                      'p-4 rounded-lg border-2 transition-all duration-200',
                      selectedDuration === '24jam'
                        ? 'border-primary bg-primary text-white'
                        : 'border-gray-200 hover:border-primary/50'
                    ]"
                  >
                    <div class="text-center">
                      <div class="font-semibold">16 jam</div>
                      <div class="text-sm opacity-90">Full Day</div>
                    </div>
                  </button>
                </div>
              </div>

              <!-- Selected Package Summary -->
              <div v-if="selectedPackage && selectedDuration" class="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 class="font-semibold text-gray-900 mb-2">Ringkasan Pemesanan</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-600">Mobil:</span>
                    <span class="font-medium">{{ car.name }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Paket:</span>
                    <span class="font-medium">{{ car.packages[selectedPackage].name }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Durasi:</span>
                    <span class="font-medium">{{ selectedDuration === '12jam' ? '12 Jam' : 'Full Day (16 Jam)' }}</span>
                  </div>
                  <div class="flex justify-between border-t border-gray-200 pt-2 mt-2">
                    <span class="text-gray-900 font-semibold">Total Harga:</span>
                    <span class="text-xl font-bold text-primary">{{ formatPrice(car.packages[selectedPackage].pricing[selectedDuration]) }}</span>
                  </div>
                </div>
              </div>

              <p class="text-sm text-gray-500">{{ car.note }}</p>
            </div>

            <!-- Features Section -->
            <div class="bg-white rounded-xl shadow-lg p-6">
              <h2 class="text-2xl font-bold text-gray-900 mb-6">Fasilitas Termasuk</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">AC dan Audio System</span>
                </div>
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">Asuransi Kendaraan</span>
                </div>
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">Kondisi Prima</span>
                </div>
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">Support 24/7</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Sidebar -->
          <div class="space-y-6">
            <!-- Booking Card -->
            <div class="bg-white rounded-xl shadow-lg p-6 sticky top-8">
              <h3 class="text-xl font-bold text-gray-900 mb-4">Pesan Sekarang</h3>
              
              <!-- Selected Package Info -->
              <div v-if="selectedPackage && selectedDuration" class="mb-6 p-4 bg-gray-50 rounded-lg">
                <div class="text-center">
                  <div class="text-sm text-gray-600 mb-1">Paket Terpilih</div>
                  <div class="font-semibold text-gray-900">{{ car.packages[selectedPackage].name }}</div>
                  <div class="text-sm text-gray-600">{{ selectedDuration === '12jam' ? '12 Jam' : 'Full Day' }}</div>
                  <div class="text-2xl font-bold text-primary mt-2">{{ formatPrice(car.packages[selectedPackage].pricing[selectedDuration]) }}</div>
                </div>
              </div>

              <!-- Booking Buttons -->
              <div class="space-y-3 mb-6">
                <button
                  @click="openWhatsApp"
                  :disabled="!selectedPackage || !selectedDuration"
                  :class="[
                    'w-full py-3 px-6 rounded-lg font-medium transition-colors',
                    selectedPackage && selectedDuration
                      ? 'bg-primary text-white hover:bg-primary-dark'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  ]"
                >
                  {{ selectedPackage && selectedDuration ? 'Pesan via WhatsApp' : 'Pilih Paket Dulu' }}
                </button>
                 <a 
              href="https://wa.me/6281325005958" 
              target="_blank"
              class="w-full border-2 border-primary text-primary py-3 px-6 rounded-lg font-medium hover:bg-primary/10 transition-colors text-center block"
            >
              Tanya Mobil Rental
            </a>
                <!-- <NuxtLink
                  to="/contact"
                  class=""
                >
                  Konsultasi Gratis
                </NuxtLink> --> 
              </div>

              <!-- Contact Info -->
              <div class="border-t border-gray-200 pt-6">
                <div class="text-center">
                  <p class="text-sm text-gray-600 mb-2">Butuh bantuan?</p>
                  <a href="tel:+6281325005958" class="text-primary font-medium hover:text-primary-dark">
                    + 6281325005958
                  </a>
                </div>
              </div>
            </div>

            <!-- Paket Wisata Terkait -->
            <!-- <div class="bg-white rounded-xl shadow-lg p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-4">Paket Wisata Terkait</h3>
              <div class="space-y-4">
                <template v-if="popularPackages.length > 0">
                  <NuxtLink 
                    v-for="paket in popularPackages.slice(0, 2)" 
                    :key="paket.id"
                    :to="`/paket-wisata-jogja/${paket.slug}/`"
                    class="border border-gray-200 rounded-lg p-4 hover:border-primary transition-colors block"
                  >
                    <div class="flex items-center gap-3">
                      <img 
                        :src="paket.image" 
                        :alt="paket.title"
                        class="w-16 h-16 object-cover rounded-lg"
                      />
                      <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-sm text-gray-900 mb-1">{{ paket.title }}</h4>
                        <div class="flex items-center gap-2 text-xs text-gray-500 mb-2">
                          <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3 text-yellow-500">
                              <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-1">{{ paket.rating }}</span>
                          </div>
                          <span>•</span>
                          <span>{{ getDurationLabel(paket.duration) }}</span>
                        </div>
                        <div class="text-sm font-medium text-primary">
                          Mulai {{ getPackagePrice(paket) }}
                        </div>
                      </div>
                    </div>
                  </NuxtLink>
                </template>
                
                <template v-else>
                  <div class="text-center py-4 text-gray-500 text-sm">
                    <p>Memuat paket wisata...</p>
                  </div>
                </template>

              
                <NuxtLink
                  to="/paket-wisata-jogja/"
                  class="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-4 rounded-lg font-medium hover:from-green-700 hover:to-green-800 transition-all duration-300 text-center block"
                >
                  <div class="flex items-center justify-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                      <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" />
                    </svg>
                    Lihat Semua Paket
                  </div>
                </NuxtLink>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import sewaMobilData from '~/data/sewa-mobil.json'
import paketWisataData from '~/data/paket-wisata.json'

const route = useRoute()
const slug = route.params.slug

// Reactive data for package selection
const selectedPackage = ref('')
const selectedDuration = ref('')

// Find car by slug
const car = computed(() => {
  return sewaMobilData.sewaMobil.find(c => c.slug === slug)
})

// Check if car exists
if (!car.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Kendaraan tidak ditemukan'
  })
}

// Get popular travel packages for cross-selling
const popularPackages = computed(() => {
  if (!paketWisataData || paketWisataData.length === 0) return []
  
  return paketWisataData
    .filter(paket => paket.rating >= 4.7)
    .sort((a, b) => {
      if (b.rating !== a.rating) {
        return b.rating - a.rating
      }
      return (b.reviews || 0) - (a.reviews || 0)
    })
    .slice(0, 3)
})

// Schema JSON-LD untuk halaman detail rental mobil - SINGLE SOURCE OF TRUTH
// CATATAN: Composable useSchemaRental.js TIDAK DIGUNAKAN di halaman ini untuk menghindari duplikasi
const schemaData = computed(() => {
  if (!car.value) return null
  
  return {
    "@context": "https://schema.org",
    "@graph": [
      // Organization Schema
      {
        "@type": "Organization",
        "@id": "https://www.jogjaliburan.com/#organization",
        "name": "3J Tour - Jogja Liburan",
        "alternateName": "3J Tour",
        "url": "https://www.jogjaliburan.com/",
        "logo": {
          "@type": "ImageObject",
          "url": "https://www.jogjaliburan.com/3j-tour-logo.webp",
          "width": 300,
          "height": 100
        },
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "+62-813-2500-5958",
          "contactType": "customer service",
          "areaServed": "ID",
          "availableLanguage": ["Indonesian"]
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Jl. Merpati, Modalan, Banguntapan, Kec. Banguntapan",
          "addressLocality": "Banguntapan",
          "addressRegion": "Kabupaten Bantul, Daerah Istimewa Yogyakarta",
          "postalCode": "55198",
          "addressCountry": "ID"
        },
        "sameAs": [
          "https://wa.me/6281325005958"
        ]
      },
      // Service Schema untuk rental mobil spesifik - ONLY ONE aggregateRating per page
      {
        "@type": "Service",
        "@id": `https://www.jogjaliburan.com/sewa-mobil-jogja/${car.value.slug}/#service`,
        "name": `Sewa ${car.value.name}`,
        "description": `${car.value.description}. Tersedia berbagai paket rental untuk kebutuhan wisata, bisnis, dan keperluan harian di Yogyakarta.`,
        "image": car.value.image,
        "url": `https://www.jogjaliburan.com/sewa-mobil-jogja/${car.value.slug}/`,
        "provider": {
          "@id": "https://www.jogjaliburan.com/#organization"
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Jl. Merpati, Modalan, Banguntapan, Kec. Banguntapan",
          "addressLocality": "Banguntapan",
          "addressRegion": "Kabupaten Bantul, Daerah Istimewa Yogyakarta",
          "postalCode": "55198",
          "addressCountry": "ID"
        },
        "areaServed": {
          "@type": "State",
          "name": "DI Yogyakarta"
        },
        "serviceType": "Car Rental",
        "category": [
          "Rental Mobil dengan Sopir",
          "Rental Mobil Lepas Kunci",
          "Sewa Mobil Wisata",
          "Rental Mobil Harian"
        ],
        "offers": car.value.packages ? Object.entries(car.value.packages).map(([key, pkg]) => ({
          "@type": "Offer",
          "name": pkg.name,
          "description": pkg.description,
          "price": pkg.pricing?.['12jam'] || "250000",
          "priceCurrency": "IDR",
          "availability": "https://schema.org/InStock",
          "validFrom": new Date().toISOString().split('T')[0],
          "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          "seller": {
            "@id": "https://www.jogjaliburan.com/#organization"
          },
          "itemOffered": {
            "@type": "Service",
            "name": `${pkg.name} - ${car.value.name}`,
            "description": pkg.description
          }
        })) : [{
          "@type": "Offer",
          "name": `Sewa ${car.value.name}`,
          "description": `Layanan rental ${car.value.name} dengan berbagai paket pilihan`,
          "price": "400000",
          "priceCurrency": "IDR",
          "availability": "https://schema.org/InStock",
          "validFrom": new Date().toISOString().split('T')[0],
          "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          "seller": {
            "@id": "https://www.jogjaliburan.com/#organization"
          },
          "itemOffered": {
            "@type": "Service",
            "name": `Sewa ${car.value.name}`,
            "description": car.value.description
          }
        }],
        "hasOfferCatalog": {
          "@type": "OfferCatalog",
          "name": `Paket Sewa ${car.value.name}`,
          "itemListElement": car.value.packages ? Object.entries(car.value.packages).map(([key, pkg]) => ({
            "@type": "Offer",
            "name": pkg.name,
            "description": pkg.description,
            "price": pkg.pricing?.['12jam'] || "250000",
            "priceCurrency": "IDR",
            "availability": "https://schema.org/InStock",
            "validFrom": new Date().toISOString().split('T')[0],
            "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            "seller": {
              "@id": "https://www.jogjaliburan.com/#organization"
            },
            "itemOffered": {
              "@type": "Service",
              "name": `${pkg.name} - ${car.value.name}`,
              "description": pkg.description
            }
          })) : []
        },
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": car.value.rating || 4.5,
          "reviewCount": car.value.reviews || 25,
          "bestRating": 5,
          "worstRating": 1,
          "itemReviewed": {
            "@type": "Product",
            "@id": `https://www.jogjaliburan.com/sewa-mobil-jogja/${car.value.slug}/#product`
          }
        }
      },
      // Product Schema untuk mobil yang disewakan - Target dari itemReviewed
      {
        "@type": "Product",
        "@id": `https://www.jogjaliburan.com/sewa-mobil-jogja/${car.value.slug}/#product`,
        "name": car.value.name,
        "description": car.value.description,
        "image": car.value.image,
        "category": "Car Rental",
        "brand": {
          "@type": "Brand",
          "name": car.value.name.split(' ')[0] || "Unknown"
        }
      },
      // Breadcrumb Schema
      {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://www.jogjaliburan.com/"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Sewa Mobil Jogja",
            "item": "https://www.jogjaliburan.com/sewa-mobil-jogja/"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": car.value.name,
            "item": `https://www.jogjaliburan.com/sewa-mobil-jogja/${car.value.slug}/`
          }
        ]
      }
    ]
  }
})

// Set default selections
onMounted(() => {
  if (car.value && car.value.packages) {
    // Set default package to first available
    const firstPackageKey = Object.keys(car.value.packages)[0]
    selectedPackage.value = firstPackageKey
    selectedDuration.value = '12jam'
  }
})

// Helper functions for cross-selling
const getDurationLabel = (duration) => {
  const labels = {
    '1-hari': '1 Hari',
    '2-hari-1-malam': '2D1N',
    '3-hari-2-malam': '3D2N',
    '4-hari-3-malam': '4D3N'
  }
  return labels[duration] || duration
}

const getPackagePrice = (paket) => {
  if (paket.pricing && paket.pricing.length > 0) {
    return paket.pricing[0].price
  }
  return paket.price || 'Rp 350.000'
}

// Format price function
const formatPrice = (price) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(price)
}

// Open WhatsApp with selected package
const openWhatsApp = () => {
  if (!car.value || !selectedPackage.value || !selectedDuration.value) return

  const packageName = car.value.packages[selectedPackage.value].name
  const duration = selectedDuration.value === '12jam' ? '12 Jam' : 'Full Day'
  const price = formatPrice(car.value.packages[selectedPackage.value].pricing[selectedDuration.value])

  const message = `Halo! 👋 Saya tertarik dengan *${car.value.name}* 🚗

📋 *Detail Booking:*
• Paket: ${packageName}
• Durasi: ${duration}
• Harga: ${price}

Bisa bantuin saya untuk:
• Konfirmasi ketersediaan
• Proses booking
• Info syarat dan ketentuan
• Detail pembayaran

Terima kasih! 🙏`

  const whatsappUrl = `https://wa.me/6281325005958?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}

// SEO metadata with schema
useHead(() => ({
  title: car.value ? `${car.value.name} - Rental Mobil Jogja Murah | 3J Tour` : 'Loading...',
  meta: [
    {
      name: 'description',
      content: car.value ? `${car.value.name} - ${car.value.description}. Tersedia paket MS, MSB, dan AIO. Harga mulai ${formatPrice(car.value.packages?.ms?.pricing?.['12jam'] || 0)}. Booking sekarang!` : 'Loading...'
    },
    {
      name: 'keywords',
      content: car.value ? `${car.value.name.toLowerCase()}, sewa mobil jogja, rental mobil yogyakarta, ${car.value.category}, paket sewa mobil` : ''
    },
    {
      property: 'og:title',
      content: car.value ? `${car.value.name} - Rental Mobil Jogja` : 'Loading...'
    },
    {
      property: 'og:description',
      content: car.value ? `${car.value.description}. Harga mulai ${formatPrice(car.value.packages?.ms?.pricing?.['12jam'] || 0)}` : 'Loading...'
    },
    {
      property: 'og:type',
      content: 'service'
    },
    {
      property: 'og:url',
      content: `https://www.jogjaliburan.com/sewa-mobil-jogja/${slug}/`
    },
    {
      property: 'og:image',
      content: car.value?.image || 'https://www.jogjaliburan.com/images/jogja-hero.webp'
    }
  ],
  link: [
    {
      rel: 'canonical',
      href: `https://www.jogjaliburan.com/sewa-mobil-jogja/${slug}/`
    }
  ],
  script: schemaData.value ? [
    {
      type: 'application/ld+json',
      children: JSON.stringify(schemaData.value)
    }
  ] : []
}))
</script>
