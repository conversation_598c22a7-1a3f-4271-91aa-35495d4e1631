// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 28/06/2025, 17.28.10
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/components/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/components/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/plugins/**/*.{js,ts,mjs}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/composables/**/*.{js,ts,mjs}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/utils/**/*.{js,ts,mjs}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;